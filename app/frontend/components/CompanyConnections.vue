<template>
  <div class="connections" :class="{ 'connections-embedded': embedded, 'connections-notification': notification }">
    <div class="section-header" v-if="!embedded">
      <h2>{{ $t('company_connections.title', '<PERSON>zvání ke spolupr<PERSON>ci') }}</h2>
    </div>
    
    <div v-if="effectivePendingContracts.length && embedded" class="flex items-center mb-3">
      <div class="flex-shrink-0 mr-3">
        <UserPlus class="text-amber-600" :size="20" />
      </div>
      <div class="flex-1">
        <span class="text-amber-700 font-bold text-lg">{{ $t('company_invitations', 'Pozvání') }}</span>
      </div>
    </div>
    
    <div class="connections-list">
      <template v-if="effectivePendingContracts.length">
        <div v-for="contract in effectivePendingContracts" 
             :key="contract.id" 
             class="connections-item">
          <div class="connections-item-content">
            <div class="flex items-center justify-between p-4 hover:bg-gray-50">
              <div class="flex items-center gap-3">
                <div class="p-2 rounded-full bg-amber-100">
                  <UserPlus :size="16" class="text-amber-600" />
                </div>
                <div class="text-sm">
                  <p class="font-medium text-gray-800">{{ contract.company.name }}</p>
                  <p v-if="contract.job_title" class="text-gray-600">{{ contract.job_title }}</p>
                  <p class="text-gray-500 text-xs">{{ new Date(contract.created_at).toLocaleDateString('cs-CZ') }}</p>
                </div>
              </div>
              <button @click="acceptConnection(contract)" class="btn btn-small btn-light">
                {{ $t('company_connections.connect', 'Připojit se') }}
                <Check class="ml-1" :size="14"/>
              </button>
            </div>
          </div>
        </div>
      </template>
      
      <div v-else-if="!embedded" class="connections-empty">
        <p class="text-muted">{{ $t('company_connections.no_invitations', 'Momentálně nemáte žádná pozvání.') }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { Check, UserPlus } from 'lucide-vue-next'
import axios from 'axios'

export default {
  name: 'CompanyConnections',
  
  components: {
    Check,
    UserPlus
  },

  props: {
    embedded: {
      type: Boolean,
      default: false
    },
    pendingContracts: {
      type: Array,
      default: () => []
    },
    notification: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      localPendingContracts: [],
      loading: false,
      error: null
    }
  },

  computed: {
    // Use prop data when embedded, otherwise use local data
    effectivePendingContracts() {
      return this.embedded ? this.pendingContracts : this.localPendingContracts;
    }
  },

  created() {
    // Only fetch data if not embedded (standalone mode)
    if (!this.embedded) {
      this.fetchPendingContracts()
    }
  },

  methods: {
    async fetchPendingContracts() {
      try {
        this.loading = true
        const response = await axios.get('/api/v1/company_connections/fetch')
        this.localPendingContracts = response.data
      } catch (err) {
        this.error = this.$t('company_connections.error', 'Nelze načíst pozvání')
        console.error('Error fetching connections:', err)
      } finally {
        this.loading = false
      }
    },

    async acceptConnection(contract) {
      if (!confirm(`${this.$t('company_connections.confirm_accept', 'Opravdu chcete přijmout pozvání od')} ${contract.company.name}?`)) {
        return
      }

      try {
        // JWT-only authentication mode - authorization handled automatically by axios interceptor
        await axios.post(`/api/v1/company_connections/${contract.id}/accept`)
        
        // Update local data based on mode
        if (this.embedded) {
          // In embedded mode, emit event to parent to handle data updates
          this.$emit('invitation-accepted', contract.id)
        } else {
          // In standalone mode, update local data
          this.localPendingContracts = this.localPendingContracts.filter(c => c.id !== contract.id)
        }
        
      } catch (err) {
        console.error('Error accepting connection:', err)
        alert(this.$t('company_connections.error_accepting', 'Nelze zpracovat pozvání. Zkuste to prosím později.'))
      }
    }
  }
}
</script>

<style>
.connections {
  max-width: 600px;
  margin: 0 auto;
}

.connections-list {
  background: white;
  border: 1px solid #eee;
  border-radius: 12px;
}

/* Embedded variant - add proper borders like other mainbox elements */
.connections-embedded .connections-list {
  background: white;
  border: 1px solid #eee;
  border-radius: 8px;
}

.connections-embedded .connections-item {
  border-bottom: 1px solid #f3f4f6;
}

.connections-embedded .connections-item:last-child {
  border-bottom: none;
}

/* Notification variant - same styling as embedded */
.connections-notification .connections-list {
  background: white;
  border: 1px solid #eee;
  border-radius: 8px;
}

.connections-notification .connections-item {
  border-bottom: 1px solid #f3f4f6;
}

.connections-notification .connections-item:last-child {
  border-bottom: none;
}

.connections-item {
  border-bottom: 1px solid #dee2e6;
}

.connections-item:last-child {
  border-bottom: none;
}

.connections-item-content {
  padding: 0;
}

.connections-item-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.connections-item-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.connections-item-head {
  display: flex;
  align-items: center;
  gap: 5px;
  padding-top: 4px;
  color: #666;
  font-size: 0.875rem;
}

.connections-item-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.connections-empty {
  padding: 1.5rem;
  text-align: center;
}

.btn-icon {
  margin-left: 0.5rem;
}

/* Embedded variant */
.connections-embedded .section-header {
  display: none;
}
</style>
