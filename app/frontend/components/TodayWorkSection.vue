<template>
  <div class="today-work-section" data-vue-component="today-work-section">
    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center items-center py-8">
      <Clock :size="24" class="animate-spin text-blue-500" />
    </div>

    <!-- Todays Activities To Work On -->
    <template v-else>
      <!-- Work Assignment Cards -->
      <div v-if="hasAssignedWorks" class="space-y-3">
        <!-- All Work Cards with Stable Order -->
        <div
          v-for="work in availableWorks"
          :key="work.id"
          :class="isWorkActive(work) ? 'p-4 rounded-lg border bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-300' : 'p-4 rounded-lg border bg-white border-gray-200'"
        >
          <div class="flex items-start justify-between">

            <!-- Todays Works to Run Activities -->
            <div class="flex items-start space-x-3">
              <!-- Status Indicator -->
              <div class="relative">
                <div v-if="isWorkActive(work)" class="w-4 h-4 bg-green-500 rounded-full flex-shrink-0"></div>
                <div v-else class="p-2 bg-blue-100 rounded-lg">
                  <LandPlot :size="16" class="text-blue-600" />
                </div>
              </div>
              
              <!-- Work Details -->
              <div class="min-w-0 flex-1">
                <div class="flex items-center gap-2 mb-1">
                  <p :class="isWorkActive(work) ? 'text-sm font-semibold text-green-900' : 'text-sm font-medium text-gray-900'">
                    {{ work.title }}
                  </p>
                </div>
                
                <!-- Activity Status for Active Work -->
                <p v-if="isWorkActive(work)" class="text-sm text-green-600 mt-0.5 flex items-center">
                  <Clock :size="12" class="mr-1" />
                  {{ formatTime(currentWorkActivity.start_time) }} - {{ formatWorkActivityType(currentWorkActivity.activity_type) }}
                </p>
                
                <!-- Time/Period Display -->
                <div v-if="work.confirmed_time || work.specific_time || work.preferred_period" 
                     :class="isWorkActive(work) ? 'text-sm mt-1 flex items-center text-green-600' : 'text-sm mt-1 flex items-center text-gray-700'">
                  <Clock :size="12" class="mr-1 flex-shrink-0" />
                  <template v-if="work.confirmed_time">
                    <span>{{ formatTime(work.confirmed_time) }}</span>
                  </template>
                  <template v-else-if="work.specific_time || work.preferred_period">
                    <span>{{ getTranslatedTime(work) }}</span>
                    <span v-if="work.specific_time" class="ml-2">
                      {{ $t('preferred_time', 'preferovaný čas') }}: {{ formatTime(work.specific_time) }}
                    </span>
                  </template>
                </div>
                
                <!-- Location -->
                <p v-if="work.location" :class="isWorkActive(work) ? 'text-sm mt-1 flex items-center text-green-600' : 'text-sm mt-1 flex items-center text-gray-700'">
                  <MapPin :size="12" class="mr-1 flex-shrink-0" />
                  <span class="break-words">{{ work.location }}</span>
                </p>

                <!-- Assigned Users -->
                <div v-if="work.work_assignments && work.work_assignments.length > 0" class="text-sm mt-1">
                  {{ work.work_assignments.map(wa => wa.contract.first_name + ' ' + wa.contract.last_name).join(', ') }}
                </div>
                
              </div>
            </div>


            

            <!-- Action Buttons -->
            <div class="flex flex-col gap-1 flex-shrink-0">
              <!-- Active Work Actions -->
              <template v-if="isWorkActive(work)">
                <button
                  @click="openEndWorkDialog"
                  class="btn btn-small btn-danger"
                >
                  {{ $t('end_work', 'Ukončit') }}
                </button>
                <button
                  @click="openWorkDetails(work)"
                  class="btn btn-small btn-secondary"
                >
                  {{ $t('details', 'Detaily') }}
                </button>
              </template>

              <!-- Inactive Work Actions -->
              <template v-else>
                <button
                  @click="quickStartWork(work)"
                  :disabled="!isWorking"
                  :class="[
                    'btn btn-small',
                    isWorking ? 'btn-light' : 'btn-disabled'
                  ]"
                >
                  {{ $t('start_work', 'Začít') }}
                </button>
                <button
                  @click="openWorkDetails(work)"
                  class="btn btn-small btn-secondary"
                >
                  {{ $t('details', 'Detaily') }}
                </button>
              </template>
            </div>
          </div>
        </div>


      </div>

      <!-- Other Activity Card (styled like Today's Work elements) -->
      <div
        v-if="hasRunningOtherActivity"
        class="p-4 rounded-lg border bg-gradient-to-r from-orange-50 to-amber-50 border-2 border-orange-300 mb-3"
      >
        <div class="flex items-start justify-between">
          <!-- Other Activity Details -->
          <div class="flex items-start space-x-3">
            <!-- Status Indicator -->
            <div class="relative">
              <div class="w-4 h-4 bg-orange-500 rounded-full flex-shrink-0"></div>
            </div>

            <!-- Activity Details -->
            <div class="min-w-0 flex-1">
              <div class="flex items-center gap-2 mb-1">
                <p class="text-sm font-semibold text-orange-900">
                  {{ runningOtherActivity.description || $t('other_works', 'Jiná zakázka') }}
                </p>
              </div>

              <!-- Activity Status -->
              <p class="text-sm text-orange-600 mt-0.5 flex items-center">
                <Clock :size="12" class="mr-1" />
                {{ formatTime(runningOtherActivity.start_time) }} - {{ $t('other_activity', 'Jiná aktivita') }}
              </p>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex flex-col gap-1 flex-shrink-0">
            <button
              @click="stopOtherActivity()"
              class="btn btn-small btn-warning text-xs px-2 py-1"
              :disabled="isProcessing"
            >
              {{ $t('stop', 'Ukončit') }}
            </button>
          </div>
        </div>
      </div>

      <!-- No Assignments Fallback -->
      <div v-else class="space-y-3 mt-3">
        <div
          :class="[
            'group p-8 bg-gradient-to-br from-gray-50 to-blue-50 rounded-lg border-2 border-dashed transition-all duration-200 text-center',
            isWorking ? 'border-gray-300 hover:border-blue-400 cursor-pointer' : 'border-gray-200 cursor-not-allowed opacity-60'
          ]"
          @click="isWorking ? openWorkActivityModal() : null"
        >
          <p class="text-sm text-gray-700">{{ $t('no_assignments_today', 'Nemáte přiřazené práce na dnes') }}</p>
          <p v-if="isWorking" class="text-sm text-blue-600 mt-2 font-medium">{{ $t('other_works', 'Klikněte pro ostatní') }}</p>
          <p v-else class="text-sm text-gray-500 mt-2">{{ $t('start_work_first', 'Nejprve zahajte pracovní den') }}</p>
        </div>
      </div>

      <!-- Other Activities Section -->
      <!-- Simple Add Other Activity Input -->
      <!-- <div class="mt-6 border-t pt-6">
        <div v-if="isWorking" class="mb-4">
          <div class="flex gap-2">
            <input
              v-model="newOtherActivityDescription"
              type="text"
              class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              :placeholder="$t('other_activity_placeholder', 'Jiná aktivita... (stiskněte Enter)')"
              @keyup.enter="addOtherActivity"
              :disabled="hasRunningOtherActivity"
              data-field="other-activity-description"
              data-testid="other-activity-input"
            />
            <button
              @click="addOtherActivity"
              :disabled="!newOtherActivityDescription.trim() || hasRunningOtherActivity"
              class="btn btn-primary"
              data-action="add-other-activity"
              data-testid="add-other-activity-button"
            >
              {{ $t('add', 'Přidat') }}
            </button>
          </div>
        </div>
      </div> -->

      <!-- Activity Modal (for manual activity entry) -->
      <div v-if="showWorkActivityModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-lg w-full">
          <h3 class="text-lg font-medium mb-4">{{ $t('select_activity', 'Vybrat zakázku') }}</h3>
          
          <div v-if="loadingWorks" class="flex justify-center py-8">
            <Clock :size="24" class="animate-spin text-gray-700" />
          </div>
          
          <div v-else class="space-y-2 max-h-96 overflow-y-auto">
            <!-- Available works -->
            <button
              v-for="work in allAssignedWorks"
              :key="work.id"
              @click="quickStartWork(work)"
              class="w-full text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors group"
            >
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <div class="font-medium">{{ work.title }}</div>
                  <div v-if="work.location" class="text-sm text-gray-700 flex items-center mt-1">
                    <MapPin :size="14" class="mr-1" />
                    {{ work.location }}
                  </div>
                </div>
                <div class="text-xs text-gray-400 group-hover:text-gray-600">
                  {{ $t('click_to_start', 'Klikněte pro zahájení') }}
                </div>
              </div>
            </button>
          </div>
          
          <div class="flex justify-end gap-2 mt-4">
            <button @click="closeWorkActivityModal" class="btn btn-secondary">
              {{ $t('cancel', 'Zrušit') }}
            </button>
          </div>
        </div>
      </div>

      <!-- End Work Dialog -->
      <div v-if="showEndWorkDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-md w-full">
          <h3 class="text-lg font-medium mb-4">{{ $t('end_work_activity', 'Ukončit pracovní aktivitu') }}</h3>
          
          <div class="mb-4">
            <p class="text-sm text-gray-600 mb-2">{{ currentWorkActivity?.work?.title }}</p>
            <textarea
              v-model="endWorkNotes"
              ref="endWorkNotesInput"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              :placeholder="$t('optional_notes_placeholder', 'Volitelné poznámky k provedené práci...')"
              rows="3"
            ></textarea>
          </div>
          
          <div class="flex justify-end gap-2">
            <button @click="cancelEndWork" class="btn btn-secondary">
              {{ $t('cancel', 'Zrušit') }}
            </button>
            <button @click="confirmEndWork" class="btn btn-primary">
              {{ $t('end_work', 'Ukončit aktivitu') }}
            </button>
          </div>
        </div>
      </div>

    </template>
  </div>
</template>

<script>
import axios from 'axios';
import { sendFlashMessage } from '@/utils/flashMessage';
import { Clock, Plus, LandPlot, MapPin, UserCircle } from 'lucide-vue-next';

export default {
  name: 'TodayWorkSection',
  components: {
    Clock,
    Plus,
    LandPlot,
    MapPin,
    UserCircle
  },
  props: {},
  data() {
    return {
      isLoading: true,
      // Working state
      isWorking: false,
      currentDailyLog: null,
      // Unified activity state - single source of truth
      allActivities: [],
      // Work activity properties
      showWorkActivityModal: false,
      assignedWorks: [], // Today's works only
      allAssignedWorks: [], // All assigned works for modal
      loadingWorks: false,
      otherActivityDescription: '',
      showEndWorkDialog: false,
      endWorkNotes: '',
      // Other Activities properties
      isProcessing: false,
      newOtherActivityDescription: ''
    };
  },
  computed: {
    hasAssignedWorks() {
      return this.availableWorks.length > 0;
    },
    availableWorks() {
      return this.assignedWorks;
    },

    // Unified activity computed properties - single source of truth
    currentActivity() {
      return this.allActivities.find(activity => !activity.end_time) || null;
    },
    currentWorkActivity() {
      return this.allActivities.find(activity => !activity.end_time && activity.work_id) || null;
    },
    workActivities() {
      return this.allActivities.filter(activity => activity.work_id);
    },
    otherActivities() {
      return this.allActivities.filter(activity => !activity.work_id);
    },

    // Time display helpers
    getTranslatedTime() {
      return (work) => {
        const timeTranslations = {
          morning: this.$t('morning', 'Dopoledne'),
          afternoon: this.$t('afternoon', 'Odpoledne'),
          allday: this.$t('all_day', 'Celý den')
        };
        return timeTranslations[work.preferred_period] || work.preferred_period;
      };
    },

    // Other Activities computed properties
    hasRunningOtherActivity() {
      return this.otherActivities.some(activity => !activity.end_time);
    },
    runningOtherActivity() {
      return this.otherActivities.find(activity => !activity.end_time) || null;
    }
  },
  methods: {
    formatTime(time) {
      return new Date(time).toLocaleTimeString('cs-CZ', {
        hour: '2-digit', 
        minute: '2-digit'
      });
    },

    formatWorkActivityType(type) {
      const typeTranslations = {
        travel_to_work: this.$t('travel', 'Cesta'),
        work_at_location: this.$t('working', 'Probíhající aktivita'),
        work_remote: this.$t('working', 'Probíhající aktivita'),
        other: this.$t('other', 'Jiné')
      };
      return typeTranslations[type] || type;
    },

    // Helper methods for work card rendering
    isWorkActive(work) {
      return this.currentWorkActivity && work.id === this.currentWorkActivity.work_id;
    },

    openWorkDetails(work) {
      console.log('Opening work details for:', work);
      const event = new CustomEvent('open-central-modal', {
        detail: {
          componentName: 'WorkForm',
          title: `${this.$t('works.edit_work', 'Upravit zakázku')}: ${work.title}`,
          props: {
            work: work
          }
        }
      });
      document.dispatchEvent(event);
    },

    handleError(error, defaultMessage) {
      console.error('Error:', error);
      const message = error.response?.data?.error || defaultMessage;
      sendFlashMessage(message, 'error');
    },

    // Work activity methods
    async openWorkActivityModal() {
      // Check if user is working
      if (!this.isWorking) {
        sendFlashMessage(this.$t('start_work_first', 'Nejprve zahajte pracovní den'), 'error');
        return;
      }

      this.showWorkActivityModal = true;
      this.otherActivityDescription = '';
      await this.fetchAllAssignedWorks(); // Fetch ALL assigned works for modal
    },
    
    closeWorkActivityModal() {
      this.showWorkActivityModal = false;
      this.otherActivityDescription = '';
      this.showEndWorkDialog = false;
      this.endWorkNotes = '';
    },
    
    openEndWorkDialog() {
      this.showEndWorkDialog = true;
      this.$nextTick(() => {
        this.$refs.endWorkNotesInput?.focus();
      });
    },
    
    cancelEndWork() {
      this.showEndWorkDialog = false;
      this.endWorkNotes = '';
    },
    
    async confirmEndWork() {
      await this.endWorkActivity(true, this.endWorkNotes);
      this.showEndWorkDialog = false;
      this.endWorkNotes = '';
    },
    
    async fetchAssignedWorks() {
      this.loadingWorks = true;
      try {
        const response = await axios.get('/api/v1/works/today', {
          headers: { 'Accept': 'application/json' }
        });
        const todaysWorks = Array.isArray(response.data) ? response.data : [];
        console.log('Today\'s works:', todaysWorks);
        
        // Preserve any works that have active activities, even if not scheduled for today
        const activeWorkIds = this.allActivities
          .filter(activity => activity.work_id && !activity.end_time)
          .map(activity => activity.work_id);
        
        const activeWorksNotInToday = this.assignedWorks.filter(work => 
          activeWorkIds.includes(work.id) && !todaysWorks.find(tw => tw.id === work.id)
        );
        
        // Combine today's works with active works not scheduled for today
        this.assignedWorks = [...todaysWorks, ...activeWorksNotInToday];
        console.log('Combined assigned works:', this.assignedWorks.map(w => w.title));
      } catch (error) {
        console.error('Error fetching today\'s works:', error);
        this.assignedWorks = [];
      } finally {
        this.loadingWorks = false;
      }
    },

    async fetchAllAssignedWorks() {
      this.loadingWorks = true;
      try {
        const response = await axios.get('/api/v1/works/assigned', {
          headers: { 'Accept': 'application/json' }
        });
        this.allAssignedWorks = Array.isArray(response.data) ? response.data : [];
        console.log('All assigned works:', this.allAssignedWorks);
        console.log('First work assignments:', this.assignedWorks[0]?.work_assignments);
      } catch (error) {
        console.error('Error fetching all assigned works:', error);
        this.allAssignedWorks = [];
      } finally {
        this.loadingWorks = false;
      }
    },
    
    // Unified data fetching - single source of truth
    async fetchDailyState() {
      try {
        const response = await axios.get('/daily_logs/current_status');
        const { last_log, activities } = response.data;

        this.allActivities = activities || [];

        if (last_log && !last_log.end_time) {
          this.currentDailyLog = last_log;
          this.isWorking = true;
        } else {
          this.currentDailyLog = null;
          this.isWorking = false;
          this.allActivities = [];
        }
      } catch (error) {
        console.error('Error fetching daily state:', error);
        this.isWorking = false;
        this.currentDailyLog = null;
        this.allActivities = [];
      }
    },

    async refreshAllData() {
      await Promise.all([
        this.fetchDailyState(),
        this.fetchAssignedWorks()
      ]);
    },

    // Unified activity management - centralized mutual exclusion
    async startActivity(params) {
      if (!this.isWorking) {
        sendFlashMessage(this.$t('start_work_first', 'Nejprve zahajte pracovní den'), 'error');
        return;
      }

      // Unified mutual exclusion
      const stopped = await this.stopCurrentActivity();
      if (!stopped) return;

      // Start new activity
      try {
        if (params.work_id) {
          // Work activity
          await axios.post('/api/v1/daily_activities/start_work_activity', {
            work_id: params.work_id,
            activity_type: params.activity_type || 'work_at_location',
            daily_log_id: this.currentDailyLog?.id
          });
        } else {
          // Other activity
          await axios.post('/api/v1/daily_activities', {
            daily_activity: {
              ...params,
              daily_log_id: this.currentDailyLog?.id,
              start_time: new Date().toISOString(),
              activity_type: 'regular'
            }
          });
        }

        await this.refreshAllData();
        this.closeWorkActivityModal();
        this.$emit('work-activity-changed');

        // Show success message
        const message = params.work_id
          ? this.$t('works.activities.work_activity_started', 'Pracovní aktivita byla zahájena')
          : this.$t('activity_started', 'Aktivita byla zahájena');
        sendFlashMessage(message, 'success');

        return true;
      } catch (error) {
        this.handleError(error, this.$t('works.activities.start_error', 'Chyba při zahájení aktivity'));
        return false;
      }
    },

    async stopCurrentActivity(notes = '') {
      if (!this.currentActivity) return true;

      try {
        const activity = this.currentActivity;
        let endpoint = `/api/v1/daily_activities/${activity.id}`;
        let payload = { daily_activity: { end_time: new Date().toISOString() } };

        // Use special work-ending endpoint if it's a work activity
        if (activity.work_id) {
          endpoint = `/api/v1/daily_activities/${activity.id}/end_work_activity`;
          payload = { notes: notes };
        } else {
          // For other activities, use the regular endpoint
          endpoint = `/daily_activities/${activity.id}`;
        }

        await axios.patch(endpoint, payload);
        return true;
      } catch (error) {
        this.handleError(error, this.$t('works.activities.end_error', 'Chyba při ukončení aktivity'));
        return false;
      }
    },

    async quickStartWork(work) {
      // If clicking on current work, do nothing
      if (this.currentWorkActivity && work.id === this.currentWorkActivity.work_id) {
        return;
      }

      // Use unified activity management
      const success = await this.startActivity({
        work_id: work.id,
        activity_type: 'work_at_location'
      });

      if (success) {
        // Add the work to today's works if it's not already there
        const workExists = this.assignedWorks.find(w => w.id === work.id);
        if (!workExists) {
          console.log('Adding work to assignedWorks:', work.title);
          this.assignedWorks.unshift(work); // Add to beginning of list
        }

        // Emit global event for other components
        document.dispatchEvent(new CustomEvent('work-activity-started', {
          detail: { activity: this.currentWorkActivity }
        }));
      }
    },
    
    async endWorkActivity(showMessage = true, notes = '') {
      if (!this.currentWorkActivity) return true; // Nothing to end, consider success

      const activityId = this.currentWorkActivity.id; // Store ID before clearing
      const stopped = await this.stopCurrentActivity(notes);

      if (stopped) {
        if (showMessage) {
          sendFlashMessage(this.$t('works.activities.work_activity_ended', 'Pracovní aktivita byla ukončena'), 'success');
          this.closeWorkActivityModal();
        }

        // Refresh data
        await this.refreshAllData();

        // Emit global event for other components
        document.dispatchEvent(new CustomEvent('work-activity-ended', {
          detail: { activityId: activityId }
        }));

        // Emit event to parent to refresh activities
        this.$emit('work-activity-changed');
      }

      return stopped;
    },

    async saveOtherActivity() {
      if (!this.otherActivityDescription.trim()) {
        return;
      }

      // Use unified activity management
      const success = await this.startActivity({
        description: this.otherActivityDescription
      });

      if (success) {
        this.otherActivityDescription = '';
      }
    },
    
    handleOtherActivityFocus() {
      // Clear the input when focusing if it's a placeholder
      if (this.currentWorkActivity) {
        // Will end current work when they type and save
      }
    },





    async initializeData() {
      this.isLoading = true;
      try {
        await this.refreshAllData();
      } catch (error) {
        console.error('Error initializing today work section:', error);
      } finally {
        this.isLoading = false;
      }
    },

    async handleGlobalWorkActivityChange(event) {
      // Refresh data when work is started from other components
      await this.refreshAllData();

      // If work was started from another component, add it to today's works
      if (event.detail && event.detail.work) {
        const work = event.detail.work;
        const workExists = this.assignedWorks.find(w => w.id === work.id);
        if (!workExists) {
          this.assignedWorks.unshift(work); // Add to beginning of list
        }
      }
    },

    async handleDailyLogChange() {
      // Refresh data when daily log starts or stops
      await this.refreshAllData();
    },

    // Other Activities methods
    isOtherActivityActive(activity) {
      return !activity.end_time;
    },


    formatOtherActivityTimeRange(activity) {
      if (!activity.start_time || !activity.end_time) return '';
      return `${this.formatTime(activity.start_time)} - ${this.formatTime(activity.end_time)}`;
    },

    async addOtherActivity() {
      if (!this.newOtherActivityDescription.trim()) return;

      this.isProcessing = true;

      try {
        // Use unified activity management
        const success = await this.startActivity({
          description: this.newOtherActivityDescription.trim()
        });

        if (success) {
          this.newOtherActivityDescription = '';
        }
      } catch (error) {
        console.error('Error creating other activity:', error);
        sendFlashMessage(this.$t('activity_start_failed', 'Nepodařilo se zahájit aktivitu'), 'error');
      } finally {
        this.isProcessing = false;
      }
    },


    async stopOtherActivity() {
      this.isProcessing = true;

      try {
        const stopped = await this.stopCurrentActivity();

        if (stopped) {
          sendFlashMessage(this.$t('activity_stopped', 'Aktivita byla ukončena'), 'success');
          await this.refreshAllData();
          this.$emit('work-activity-changed');
        }
      } catch (error) {
        console.error('Error stopping other activity:', error);
        sendFlashMessage(this.$t('activity_stop_failed', 'Nepodařilo se ukončit aktivitu'), 'error');
      } finally {
        this.isProcessing = false;
      }
    }
  },

  async mounted() {
    await this.initializeData();

    // Listen for work activity events from other components
    document.addEventListener('work-activity-started', this.handleGlobalWorkActivityChange);

    // Listen for daily log events from TimeTracking component
    document.addEventListener('daily-log-started', this.handleDailyLogChange);
    document.addEventListener('daily-log-ended', this.handleDailyLogChange);
  },

  beforeUnmount() {
    document.removeEventListener('work-activity-started', this.handleGlobalWorkActivityChange);
    document.removeEventListener('daily-log-started', this.handleDailyLogChange);
    document.removeEventListener('daily-log-ended', this.handleDailyLogChange);
  },

  watch: {
    isWorking: {
      handler(newVal) {
        if (newVal) {
          // When work starts, refresh all data
          this.refreshAllData();
        } else {
          // When work ends, clear all activities
          this.allActivities = [];
        }
      },
      immediate: true
    }
  }
};
</script>

<style scoped>
/* Removed duplicated button styles - using global button system from application.css */

</style>
