// ABOUTME: Minimal event display component for calendar grid with drag-drop support
// ABOUTME: Shows event title, name in second row, and highlights unapproved events

<template>
  <div
    :class="['calendar-event-item', statusClass, {
      'is-draggable': isDraggable,
      'is-compact': isCompact,
      'needs-attention': needsAttention,
      'contract-event': isContractEvent,
      'event-rejected': event.status === 'rejected'
    }]"
    @click="handleClick"
    :title="eventTooltip"
  >
    <div class="event-icon">
      <BellRing v-if="event.event_type === 'vacation' && event.status === 'pending'" :size="14" />
      <component v-else :is="eventIcon" :size="14" />
    </div>
    <div class="event-content">
      <div v-if="!isCompact && getEventName()" class="event-name" :class="{ 'rejected-text': event.status === 'rejected' }">
        {{ getEventName() }}
      </div>
      <div class="event-title" :class="{ 'rejected-text': event.status === 'rejected' }">
      </div>
    </div>
  </div>
</template>

<script>
import { Calendar, Users, User, CalendarDays, Ban, BellRing, Move, Hand, Cross, Stethoscope, CalendarX, Plane, Car, TreePalm, Luggage } from 'lucide-vue-next';

export default {
  name: 'CalendarEventItem',
  components: {
    Calendar,
    Users,
    User,
    Ban,
    BellRing,
    CalendarDays,
    Move,
    Hand,
    Cross,
    Stethoscope,
    CalendarX,
    Plane,
    Car, 
    TreePalm, 
    Luggage
  },
  props: {
    event: {
      type: Object,
      required: true
    },
    isDraggable: {
      type: Boolean,
      default: true
    },
    isCompact: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    statusClass() {
      // Base class for all events
      const baseClass = 'event-base';

      // Check if event is rejected
      if (this.event.status === 'rejected') {
        return `${baseClass} event-rejected-status`;
      }

      // Check if event is cancelled or finished
      if (this.event.status === 'cancelled' || this.event.status === 'finished') {
        return `${baseClass} event-inactive`;
      }

      return `${baseClass} event-active`;
    },

    needsAttention() {
      // Only vacation events that are not approved should shine/highlight
      return this.event.event_type === 'vacation' && (this.event.status === 'pending' || this.event.status === 'draft');
    },
    
    showTime() {
      // Don't show time for all-day events or in compact mode
      if (this.isCompact) return false;
      const startTime = new Date(this.event.start_time);
      return !(startTime.getHours() === 0 && startTime.getMinutes() === 0);
    },
    
    formatTime() {
      const startTime = new Date(this.event.start_time);
      return startTime.toLocaleTimeString(this.$i18n.locale, {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    },
    
    showType() {
      return !this.isCompact && this.event.event_type;
    },
    
    eventIcon() {
      // Map event_type enum values to appropriate icons
      const iconMap = {
        0: 'Cross',      // illness
        1: 'Stethoscope',       // day_care
        2: 'Stethoscope',      // family_sick
        3: 'CalendarX', // other
        4: 'TreePalm',      // vacation
        5: 'Luggage'      // travel
      };

      // Handle both numeric and string event types
      const eventType = this.event.event_type;

      // If it's a numeric enum value, use the mapping
      if (typeof eventType === 'number' && iconMap[eventType]) {
        return iconMap[eventType];
      }

      // Handle string-based event types (legacy support)
      if (typeof eventType === 'string') {
        const stringIconMap = {
          'illness': 'Cross',
          'day_care': 'Stethoscope',
          'family_sick': 'Stethoscope',
          'other': 'CalendarX',
          'vacation': 'TreePalm',
          'travel': 'Luggage',
          'meeting': 'Users',
          'personal': 'User'
        };

        if (stringIconMap[eventType]) {
          return stringIconMap[eventType];
        }
      }

      // Default fallback icon
      return 'Calendar';
    },
    
    eventTooltip() {
      const parts = [this.event.title || this.event.name];
      if (this.showTime) parts.push(this.formatTime);
      if (this.event.location) parts.push(this.event.location);
      if (this.isContractEvent) parts.push('(Contract event)');
      return parts.join(' - ');
    },
    
    isContractEvent() {
      // Contract events have contract information from other users
      return this.event.contract && this.event.contract.first_name && this.event.contract.last_name;
    }
  },
  methods: {
    getEventTitle() {
      // Try various fields that might contain the event title
      return this.event.t_event_type || 
             this.event.title || 
             this.event.name || 
             this.event.description ||
             `Event ${this.event.id}`;
    },

    getEventName() {
      // Get the name/person associated with this event (second row)
      return this.event.name || 
             (this.event.contract ? `${this.event.contract.first_name} ${this.event.contract.last_name}` : null);
    },
    
    handleClick() {
      this.$emit('click', this.event);
      
      // Could also emit edit request if needed
      if (this.isDraggable) {
        this.$emit('edit-request', this.event);
      }
    }
  }
};
</script>
<style scoped>
/*
 * OPTIMIZATION NOTES:
 * 1. Removed `box-shadow` from hover and drag states. It's very expensive to render.
 * 2. Removed the `linear-gradient` striped background. It requires a lot of "paint" work from the browser when many are on screen.
 * 3. Changed `transition: all` to be specific, which is more efficient.
 * 4. Enhanced the `transform` on the drag state to give feedback without the performance cost of a shadow.
*/

.calendar-event-item {
  position: relative;
  padding: 0.25rem;
  margin-bottom: 0.125rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: transform 0.2s ease;
  display: flex;
  align-items: flex-start;
  min-height: 1.75rem;
  overflow: hidden;
  gap: 0.15rem;
  border: 1px solid #faaab2;
  background-color: white;
}

.calendar-event-item:hover {
  transform: translateY(1px);
}

.calendar-event-item.is-compact {
  padding: 0.125rem 0.375rem;
  font-size: 0.75rem;
  min-height: 1.25rem;
}

.event-base {
  background-color: white;
  color: #333;
}

.event-active {
  background-color: white;
    color: #333;
}

.event-inactive {
  background-color: white;
  color: #777;
}

.calendar-event-item.is-draggable {
  cursor: grab;
  background-color: white;
}

.calendar-event-item.needs-attention {
  --attention-bg: #fefce8;
  --attention-border: #facc15;
  color: #3f3f46;
  border-left: 3px solid var(--attention-border);
  background-color: var(--attention-bg);
}

.event-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  min-width: 0;
}

.event-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.event-title {
  font-size: 0.75rem;
  opacity: 0.9;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

/* Dragging state - Optimized for fluid motion */
.calendar-event-item.sortable-ghost {
  opacity: 0.4;
  background: #e0e7ff; /* Added a subtle background to better indicate the drop zone */
}

.calendar-event-item.sortable-drag {
  cursor: grabbing;
  opacity: 0.9;
  /* A slightly larger scale provides a clear "lifted" effect without a shadow. */
  transform: scale(1.05);
}

/* Rejected event styling */
.event-rejected:hover {
  border-color: #ef5350;
  opacity: 0.9;
}

.event-rejected-status {
  background-color: #ffebee;
}

.rejected-text {
  text-decoration: line-through;
  color: #757575 !important;
}

.status-rejected {
  font-size: 0.6rem;
  font-weight: 600;
  color: #c62828;
  text-transform: uppercase;
  background-color: #ffebee;
  padding: 1px 4px;
  border-radius: 2px;
  margin-top: 2px;
  align-self: flex-start;
}

.drag-handle-icon {
  position: absolute;
  top: 0.125rem;
  right: 0.125rem;
  color: #8b5cf6; /* Saturated purple */
  opacity: 0.6;
}
</style>