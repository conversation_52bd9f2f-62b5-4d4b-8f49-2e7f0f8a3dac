<!-- ABOUTME: Small card component for displaying unscheduled works in the sidebar for drag-and-drop scheduling -->
<!-- ABOUTME: Provides basic work information to help with scheduling decisions -->

<template>
  <div class="unscheduled-work-card">
    <div class="work-header">
      <div class="work-title-section">
        <LandPlot :size="14" class="detail-icon" />
        <div class="work-title">{{ work.title }}</div>
      </div>
    </div>

    <div class="work-details">
      <div v-if="work.location" class="detail-item">
        <MapPin :size="12" class="detail-icon" />
        <span class="detail-text">{{ work.location }}</span>
      </div>

      <div class="detail-item">
        <Users :size="12" class="detail-icon" />
        <div v-if="assignedPeople.length > 0" class="assignments-list">
          <span
            v-for="(person, index) in assignedPeople"
            :key="person.id"
            class="assignment-name"
          >
            {{ person.first_name }} {{ person.last_name }}{{ index < assignedPeople.length - 1 ? ', ' : '' }}
          </span>
        </div>
        <span v-else class="detail-text">{{ $t('works.assignments.no_assignments', 'Žádná přiřazení') }}</span>
      </div>

      <div v-if="work.expected_duration" class="detail-item">
        <Clock :size="12" class="detail-icon" />
        <span class="detail-text">{{ work.expected_duration }}h</span>
      </div>
    </div>

    <div class="work-badges">
      <span v-if="work.status" class="status-badge" :class="work.status">
        {{ $t(`works.status.${work.status}`, work.status) }}
      </span>
      <span v-if="work.priority" class="status-badge priority-badge" :class="`priority-${work.priority}`">
        {{ $t(`priority.${work.priority}`, work.priority) }}
      </span>
    </div>
  </div>
</template>

<script>
import { Clock, MapPin, Users, LandPlot } from 'lucide-vue-next';

export default {
  name: 'UnscheduledWorkCard',
  components: {
    Clock,
    MapPin,
    Users,
    LandPlot
  },
  props: {
    work: {
      type: Object,
      required: true
    }
  },
  computed: {
    priorityClass() {
      return `priority-${this.work.priority}`;
    },
    
    statusClass() {
      return `status-${this.work.status}`;
    },
    
    assignedPeople() {
      // Works have work_assignments array with contract relationships
      if (this.work.work_assignments && this.work.work_assignments.length > 0) {
        return this.work.work_assignments.map(assignment => assignment.contract).filter(Boolean);
      }
      return [];
    },
    
  },
  methods: {
    formatCreatedDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString(this.$i18n.locale, { 
        month: 'short', 
        day: 'numeric' 
      });
    },
    
    truncateText(text, maxLength) {
      if (!text) return '';
      if (text.length <= maxLength) return text;
      return text.substring(0, maxLength) + '...';
    }
  }
};
</script>

<style scoped>
.unscheduled-work-card {
  background-color: white;
  border: 1px solid #3b82f6;
  border-radius: 0.375rem;
  padding: 0.5rem;
  margin-bottom: 0.25rem;
  transition: transform 0.2s ease;
  cursor: grab;
}

.unscheduled-work-card:active { cursor: grabbing; }

.work-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.work-title-section {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  flex: 1;
}

.work-title {
  font-weight: 600;
  margin: 0;
  line-height: 1.4;
}

.work-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.detail-item { 
  display: flex;
  align-items: center;
  gap: 0.25rem; 
}
.detail-icon { 
  color: #1f2937;
  flex-shrink: 0;
}
.detail-text {
  font-size: 0.9rem;
  color: #1f2937;
  font-weight: 500;
}

.assignments-list {
  display: flex;
  flex-wrap: wrap;
}
.assignment-name {
  font-size: 0.9rem;
  color: #374151;
}
.work-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  justify-content: flex-end;
}
.status-badge {
  font-size: 0.6875rem;
  padding: 0.125rem 0.375rem;
  border-radius: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

/* Status badge colors to match WorkShow */
.scheduled {
  background-color: #e1f5fe;
  color: #0277bd;
}
.in_progress {
  background-color: #fff3e0;
  color: #f57c00;
}
.completed {
  background-color: #e8f5e8;
  color: #2d7d32;
}
.cancelled {
  background-color: #fce4ec;
  color: #c2185b;
}
.rescheduled {
  background-color: #fff3e0;
  color: #f57c00;
}
.unprocessed {
  background-color: #f3f4f6;
  color: #374151;
}
.inactive {
  background-color: #f5f5f5;
  color: #757575;
}
.pending {
  background-color: #fefce8;
  color: #854d0e;
}
.confirmed {
  background-color: #e8f5e8;
  color: #2d7d32;
}

/* Priority visual as secondary badge */
.priority-badge {
  background-color: #f3f4f6;
  color: #374151;
}
.priority-low {
  background: #f0f9ff;
  color: #0369a1;
}
.priority-medium {
  background: #fffbeb;
  color: #d97706;
}
.priority-high {
  background: #fef2f2;
  color: #dc2626;
}
.priority-urgent {
  background: #7c2d12;
  color: white;
}

/* Dragging styles */
.sortable-ghost {
  opacity: 0.4;
}
.sortable-drag {
  opacity: 0;
}

</style>
