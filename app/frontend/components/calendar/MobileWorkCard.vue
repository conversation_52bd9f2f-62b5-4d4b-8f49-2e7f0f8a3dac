<!-- ABOUTME: Mobile-optimized work card component for touch-friendly scheduling interface -->
<!-- ABOUTME: Displays unscheduled works with tap-to-select functionality in mobile unscheduled panel -->

<template>
  <div
    class="mobile-work-card"
    :class="{ 'selected': isSelected }"
    data-vue-component="mobile-work-card"
  >
    <div class="work-header">
      <div class="work-title-section">
        <LandPlot :size="14" class="detail-icon" />
        <h4 class="work-title">{{ work.title }}</h4>
      </div>
    </div>

    <div class="work-details">
      <div v-if="formatWorkTime()" class="detail-item">
        <Clock :size="12" class="detail-icon" />
        <span class="detail-text">{{ formatWorkTime() }}</span>
      </div>

      <div v-if="work.location" class="detail-item">
        <MapPin :size="12" class="detail-icon" />
        <span class="detail-text">{{ work.location }}</span>
      </div>

      <div v-if="assignedPeople && assignedPeople.length" class="detail-item">
        <Users :size="12" class="detail-icon" />
        <div class="assignments-list">
          <span
            v-for="(person, index) in assignedPeople"
            :key="person.id"
            class="assignment-name"
          >
            {{ person.first_name }} {{ person.last_name }}{{ index < assignedPeople.length - 1 ? ', ' : '' }}
          </span>
        </div>
      </div>

      <div v-if="work.estimated_duration || work.expected_duration" class="detail-item">
        <Clock :size="12" class="detail-icon" />
        <span class="detail-text">{{ formatDuration(work.estimated_duration || work.expected_duration) }}</span>
      </div>
    </div>

    <div class="work-badges">
      <span v-if="work.status" class="status-badge" :class="work.status">
        {{ formatStatus(work.status) }}
      </span>
      <span v-if="work.priority" class="status-badge priority-badge" :class="`priority-${work.priority}`">
        {{ formatPriority(work.priority) }}
      </span>
    </div>
  </div>
</template>

<script>
import { MapPin, Users, Clock, CheckCircle, LandPlot } from 'lucide-vue-next';

export default {
  name: 'MobileWorkCard',
  components: {
    MapPin,
    Users,
    Clock,
    CheckCircle,
    LandPlot
  },
  props: {
    work: {
      type: Object,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    assignedPeople() {
      if (this.work && this.work.work_assignments && this.work.work_assignments.length) {
        return this.work.work_assignments.map(a => a.contract).filter(Boolean);
      }
      return [];
    }
  },
  methods: {
    formatPriority(priority) {
      const priorities = {
        low: this.$t('priority.low', 'Nízká'),
        medium: this.$t('priority.medium', 'Střední'),
        high: this.$t('priority.high', 'Vysoká'),
        urgent: this.$t('priority.urgent', 'Urgentní')
      };
      return priorities[priority] || priority;
    },

    formatStatus(status) {
      const statuses = {
        scheduled: this.$t('works.scheduled_work', 'Naplánovaná zakázka'),
        in_progress: this.$t('works.in_progress_work', 'Zakázka probíhá'),
        completed: this.$t('works.completed_work', 'Dokončená zakázka'),
        cancelled: this.$t('works.cancelled_work', 'Zrušená zakázka'),
        rescheduled: this.$t('works.booking_change_notice', 'Pozor, změna rezervace'),
        unprocessed: this.$t('works.unprocessed_work', 'Nová zakázka')
      };
      return statuses[status] || status;
    },

    formatWorkTime() {
      if (this.work.confirmed_time) {
        return this.formatTime(this.work.confirmed_time);
      }
      if (this.work.specific_time) {
        return this.formatTime(this.work.specific_time);
      }
      if (this.work.preferred_period) {
        const periods = {
          morning: this.$t('morning', 'Dopoledne'),
          afternoon: this.$t('afternoon', 'Odpoledne'),
          allday: this.$t('all_day', 'Celý den')
        };
        return periods[this.work.preferred_period] || this.work.preferred_period;
      }
      return '';
    },

    formatTime(dateString) {
      const date = new Date(dateString);
      return date.toLocaleTimeString('cs-CZ', {
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    getAssignedCount() {
      if (this.work.assigned_users_count) {
        return this.work.assigned_users_count;
      }
      if (this.work.work_assignments && this.work.work_assignments.length) {
        return this.work.work_assignments.length;
      }
      return 0;
    },

    formatDuration(minutes) {
      if (!minutes) return '';
      if (minutes < 60) {
        return `${minutes}min`;
      }
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;

      if (remainingMinutes === 0) {
        return `${hours}h`;
      }
      return `${hours}h ${remainingMinutes}min`;
    },

    truncateDescription(description) {
      if (!description) return '';
      return description.length > 80
        ? description.substring(0, 80) + '...'
        : description;
    },
    
  }
};
</script>

<style scoped>
.mobile-work-card {
  background-color: white;
  border: 1px solid #3b82f6;
  border-radius: 0.375rem;
  padding: 0.5rem;
  margin-bottom: 0.25rem;
  transition: transform 0.2s ease;
  user-select: none;
  touch-action: manipulation;
}

.mobile-work-card.selected {
  background: #3b83f620;
}

.work-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.work-title-section {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  flex: 1;
}

.work-title {
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  line-height: 1.4;
  font-size: 0.9rem;
}

.work-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.detail-icon {
  color: #6b7280;
  flex-shrink: 0;
}

.detail-text {
  font-size: 0.9rem;
  color: #374151;
  font-weight: 500;
}

.work-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  justify-content: flex-end;
}

.status-badge {
  font-size: 0.6875rem;
  padding: 0.125rem 0.375rem;
  border-radius: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

/* Status badge colors to match WorkShow */
.scheduled { background-color: #e1f5fe; color: #0277bd; }
.in_progress { background-color: #fff3e0; color: #f57c00; }
.completed { background-color: #e8f5e8; color: #2d7d32; }
.cancelled { background-color: #fce4ec; color: #c2185b; }
.rescheduled { background-color: #fff3e0; color: #f57c00; }
.unprocessed { background-color: #f3f4f6; color: #374151; }
.inactive { background-color: #f5f5f5; color: #757575; }
.pending { background-color: #fefce8; color: #854d0e; }
.confirmed { background-color: #e8f5e8; color: #2d7d32; }

/* Priority visual as secondary badge */
.priority-badge { background-color: #f3f4f6; color: #374151; }
.priority-low { background: #f0f9ff; color: #0369a1; }
.priority-medium { background: #fffbeb; color: #d97706; }
.priority-high { background: #fef2f2; color: #dc2626; }
.priority-urgent { background: #7c2d12; color: white; }

/* Assignments formatting to match WorkShow */
.assignments-list { display: flex; flex-wrap: wrap; }
.assignment-name { font-size: 0.9rem; color: #374151; }

/* Mobile tweaks */
@media (max-width: 768px) {
  .mobile-work-card { padding: 0.375rem; }
  .work-title { font-size: 0.875rem; }
  .detail-text { font-size: 0.875rem; }
  .status-badge { font-size: 0.75rem; padding: 0.3rem 0.5rem; }
}
</style>
