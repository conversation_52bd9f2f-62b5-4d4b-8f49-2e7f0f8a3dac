<template>
  <div class="new-calendar-layout">
    <div class="left-pane">
      <!-- This is a placeholder for future use, currently not visible -->
    </div>

    <div class="calendar-main-content">
      <div class="calendar-header">
        <div class="nav-control">
          <button @click="prevMonth" class="text-link">
            <ChevronLeft :size="20" />
          </button>
          <h2>{{ capitalizedMonthYear }}</h2>
          <button @click="nextMonth" class="text-link">
            <ChevronRight :size="20" />
          </button>
        </div>
      </div>

      <div class="days-of-week-header">
        <div v-for="day in daysOfWeek" :key="day" class="weekday-name">
          {{ day }}
        </div>
      </div>

      <div class="calendar-grid">
        <div v-for="(day, index) in calendarDays" :key="index" :class="['calendar-day-cell', { 
          'not-current-month': !day.isCurrentMonth, 
          'weekend': day.isCurrentMonth && isWeekend(day.date),
          'holiday': day.isCurrentMonth && isHoliday(day.date),
          'today': day.isCurrentMonth && isToday(day.date)
        }]" 
        @click="handleDayClick(day, $event)"
        :data-testid="day.isCurrentMonth ? `calendar-day-${day.day}` : null">
          <div v-if="day.day" class="day-number-container">
            <span class="day-number">{{ day.day }}</span>
          </div>
          
          
          <!-- Create buttons are now rendered globally outside the calendar grid -->
          
          <!-- Full drag-and-drop (now available to all users) -->
          <draggable
            v-if="day.isCurrentMonth && day.date"
            v-model="itemsByDateLocal[formatDateKey(day.date)]"
            :group="{ name: 'calendar-items', pull: true, put: true }"
            item-key="uniqueId"
            class="day-content drop-zone"
            :data-date="formatDateKey(day.date)"
            @start="onDragStart"
            @end="onDragEnd"
            @change="onListChange($event, formatDateKey(day.date))"
            :disabled="loadingStates.update"
          >
            <template #item="{ element }">
              <CalendarEventItem 
                v-if="element.itemType === 'event'"
                :event="element"
                :is-draggable="canDragItem(element, 'event')"
                :is-compact="isCompactView"
                @click="handleItemClick(element, 'event')"
              />
              <CalendarWorkItem 
                v-else-if="element.itemType === 'work' || (element.id && element.title && element.company_id)"
                :work="element"
                :is-draggable="canDragItem(element, 'work')"
                :is-compact="isCompactView"
                @click="handleItemClick(element, 'work')"
              />
              <div v-else class="unknown-item">
                Unknown item: {{ element.itemType || 'no itemType' }} (ID: {{ element.id }})
              </div>
            </template>
          </draggable>
        </div>
      </div>
      
    </div>

    <div class="right-sidebar-pane">
      <AdvancedFeature 
        feature="work_planning"
        title="Work Planning"
        description="Drag and drop works to schedule them easily"
      >
        <CalendarSidebar 
          @create-work="handleCreateWork"
          @create-event="handleCreateEvent"
          @work-clicked="handleWorkClicked"
          @event-clicked="handleEventClicked"
          @item-updated="handleItemUpdated"
        />
      </AdvancedFeature>
    </div>

    <!-- Global Create Buttons Container (positioned outside grid to avoid clipping) -->
    <div v-if="showCreateButtons" 
         ref="globalCreateButtons"
         class="global-day-create-buttons"
         data-testid="day-create-buttons"
         :style="globalButtonsStyle">
      <button @click="createEventForSelectedDay" 
              class="btn btn-outline"
              data-testid="create-event-btn">
        {{ $t('events.new', 'Událost') }}
      </button>
      <button @click="createWorkForSelectedDay" 
              class="btn btn-outline"
              data-testid="create-work-btn">
        {{ $t('works.new', 'Zakázka') }}
      </button>
    </div>

    <!-- Work Form Modal -->
    <div v-if="showWorkForm" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>{{ $t('works.new', 'Nová zakázka') }}</h3>
          <button @click="closeWorkForm" class="close-btn">×</button>
        </div>
        <div class="central-modal-content">
          <WorkForm 
            :initial-date="selectedDate"
            @cancel="closeWorkForm" 
            @saved="workSaved" />
        </div>
      </div>
    </div>

    <!-- Event Form Modal -->
    <div v-if="showEventForm" class="modal-overlay" @click.self="closeEventForm">
      <div class="modal-container">
        <div class="modal-header">
          <h3>{{ $t('events.new', 'Nová událost') }}</h3>
          <button @click="closeEventForm" class="close-btn">×</button>
        </div>
        <div class="central-modal-content">
          <EventForm 
            :initial-date="selectedDate"
            @event-added="eventAdded" 
            @cancel="closeEventForm" 
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ChevronLeft, ChevronRight, CalendarPlus, FolderPlus } from 'lucide-vue-next';
import { mapState, mapGetters, mapActions } from 'vuex';
import draggable from 'vuedraggable';
import CalendarEventItem from '../calendar/CalendarEventItem.vue';
import CalendarWorkItem from '../calendar/CalendarWorkItem.vue';
import CalendarSidebar from '../calendar/CalendarSidebar.vue';
import AdvancedFeature from '../AdvancedFeature.vue';
import authorizationMixin from '../../mixins/authorizationMixin.js';
import WorkForm from '../works/WorkForm.vue';
import EventForm from '../events/EventForm.vue';

export default {
  name: 'NewMonthlyCalendar',
  components: {
    ChevronLeft,
    ChevronRight,
    CalendarPlus,
    FolderPlus,
    CalendarEventItem,
    CalendarWorkItem,
    CalendarSidebar,
    AdvancedFeature,
    WorkForm,
    EventForm,
    draggable,
  },
  mixins: [authorizationMixin],
  props: {
    holidays: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      currentDate: new Date(),
      isCompactView: false, // Can be toggled later
      itemsByDateLocal: {}, // Local structure for drag-drop
      showWorkForm: false, // Control work form modal
      showEventForm: false, // Control event form modal
      selectedDate: null, // Store selected date for event creation
      draggedFromSidebar: null, // Store item being dragged from sidebar
      showCreateButtons: null, // Track which day should show create buttons
      globalButtonsStyle: {}, // Dynamic positioning for global buttons
    };
  },
  created() {
    // Initialize the local structure immediately
    this.initializeEmptyStructure();
    // Fetch calendar data when component is created
    this.fetchCalendarData(this.currentDate);
    
    // Add global click listener to hide create buttons
    document.addEventListener('click', this.handleGlobalClick);
    // Add ESC key listener for modals
    document.addEventListener('keydown', this.handleEscKey);
  },
  beforeUnmount() {
    // Remove global click listener
    document.removeEventListener('click', this.handleGlobalClick);
    // Remove ESC key listener
    document.removeEventListener('keydown', this.handleEscKey);
  },
  watch: {
    events: {
      handler(newEvents, oldEvents) {
        this.updateLocalItemsStructure();
      },
      deep: true
    },
    works: {
      handler(newWorks, oldWorks) {
        this.updateLocalItemsStructure();
      },
      deep: true
    }
  },
  computed: {
    ...mapState('calendarStore', ['events', 'works', 'loadingStates']),
    ...mapGetters('calendarStore', ['itemsByDate', 'canEditItem']),
    
    capitalizedMonthYear() {
      const str = this.currentDate.toLocaleDateString(this.$i18n.locale, { month: 'long', year: 'numeric' });
      return str.charAt(0).toUpperCase() + str.slice(1);
    },
    daysOfWeek() {
      const formatter = new Intl.DateTimeFormat(this.$i18n.locale, { weekday: 'short' });
      // Create a date that is a Monday to start the week correctly
      const monday = new Date('2023-01-02');
      return Array.from({ length: 7 }, (_, i) => {
        const day = new Date(monday);
        day.setDate(monday.getDate() + i);
        return formatter.format(day);
      });
    },
    calendarDays() {
      const year = this.currentDate.getFullYear();
      const month = this.currentDate.getMonth();
      
      const firstDayOfMonth = new Date(year, month, 1);
      const lastDayOfMonth = new Date(year, month + 1, 0);
      
      const daysInMonth = lastDayOfMonth.getDate();
      
      // Adjust to get Monday as 0, Sunday as 6
      let startDayOfWeek = firstDayOfMonth.getDay();
      startDayOfWeek = startDayOfWeek === 0 ? 6 : startDayOfWeek - 1;

      const grid = [];
      
      // Add empty cells for days before the start of the month
      for (let i = 0; i < startDayOfWeek; i++) {
        grid.push({ day: null, isCurrentMonth: false });
      }
      
      // Add cells for each day of the month
      for (let day = 1; day <= daysInMonth; day++) {
        grid.push({ day: day, isCurrentMonth: true, date: new Date(year, month, day) });
      }
      
      // Add empty cells to complete the grid
      while (grid.length % 7 !== 0) {
        grid.push({ day: null, isCurrentMonth: false });
      }
      
      return grid;
    }
  },
  methods: {
    ...mapActions('calendarStore', ['fetchCalendarData', 'handleDragEnd']),
    
    // Check if a date is today
    isToday(date) {
      if (!date || !(date instanceof Date)) return false;
      const today = new Date();
      return date.toDateString() === today.toDateString();
    },
    
    isHoliday(date) {
      if (!date) return false;
      const dateString = date.toLocaleDateString('en-CA');
      return this.holidays.includes(dateString);
    },
    isWeekend(date) {
      if (!date) return false;
      const dayOfWeek = date.getDay();
      return dayOfWeek === 0 || dayOfWeek === 6; // Sunday (0) or Saturday (6)
    },
    prevMonth() {
      const newDate = new Date(this.currentDate);
      newDate.setMonth(newDate.getMonth() - 1);
      this.currentDate = newDate;
      this.fetchCalendarData(this.currentDate); // Refresh data for new month
    },
    nextMonth() {
      const newDate = new Date(this.currentDate);
      newDate.setMonth(newDate.getMonth() + 1);
      this.currentDate = newDate;
      this.fetchCalendarData(this.currentDate); // Refresh data for new month
    },
    
    getItemsForDate(date) {
      if (!date) return { events: [], works: [], meetings: [] };
      return this.itemsByDate(date);
    },
    
    canDragItem(item, itemType) {
      // Work scheduling now available to all users
      return this.canEditItem(item, itemType);
    },
    
    handleItemClick(item, itemType) {
      // Show item detail in sidebar (work scheduling now available to all users)  
      this.$store.dispatch('calendarStore/showItemDetail', { item, itemType });
    },
    
    
    // Initialize empty structure for all days in the month
    initializeEmptyStructure() {
      const structure = {};
      
      this.calendarDays.forEach(day => {
        if (day.isCurrentMonth && day.date) {
          const dateKey = this.formatDateKey(day.date);
          structure[dateKey] = [];
        }
      });
      
      this.itemsByDateLocal = structure;
    },
    
    // Data structure methods
    updateLocalItemsStructure() {
      const structure = {};
      
      // Initialize all days in current month
      this.calendarDays.forEach(day => {
        if (day.isCurrentMonth && day.date) {
          const dateKey = this.formatDateKey(day.date);
          structure[dateKey] = [];
        }
      });
      
      // Add events with unique IDs and type
      if (this.events) {
        this.events.forEach(event => {
          const eventDates = this.getEventDates(event);
          eventDates.forEach(dateKey => {
            if (structure[dateKey]) {
              structure[dateKey].push({
                ...event,
                itemType: 'event',
                uniqueId: `event-${event.id}`
              });
            }
          });
        });
      }
      
      // Add works with unique IDs and type
      if (this.works) {
        this.works.forEach(work => {
          const workDates = this.getWorkDates(work);
          workDates.forEach(dateKey => {
            if (structure[dateKey]) {
              structure[dateKey].push({
                ...work,
                itemType: 'work',
                uniqueId: `work-${work.id}`
              });
            }
          });
        });
      }

      
      // Replace the entire object to ensure reactivity in Vue 3
      this.itemsByDateLocal = { ...structure };
    },
    
    getItemsForDateAsList(date) {
      const dateKey = this.formatDateKey(date);
      return this.itemsByDateLocal[dateKey] || [];
    },
    
    onListChange(event, dateKey) {
      console.log('📅 List change event:', event, 'for date:', dateKey);
      
      // Handle item added (from sidebar or moved from another day)
      if (event.added) {
        const addedItem = event.added.element;
        console.log('📋 Item added:', addedItem.title || addedItem.name || `ID: ${addedItem.id}`);
        
        // Check if this is an unscheduled work being dropped from sidebar
        // Detect by checking if it has work structure but no scheduled_start_date
        if (addedItem.id && addedItem.title && !addedItem.scheduled_start_date && addedItem.company_id) {
          console.log('🚀 Scheduling unscheduled work:', addedItem.title, 'for date:', dateKey);
          
          // Remove the item from the local array immediately to prevent UI issues
          const currentList = this.itemsByDateLocal[dateKey] || [];
          const itemIndex = currentList.findIndex(item => item.id === addedItem.id);
          if (itemIndex !== -1) {
            currentList.splice(itemIndex, 1);
          }
          
          // Add itemType to the work for consistency
          const workWithType = { ...addedItem, itemType: 'work' };
          
          // Schedule the work
          this.scheduleUnscheduledWork(workWithType, dateKey);
        }
        // Handle regular drag between calendar days (items that already have dates and are formatted)
        else if (addedItem.itemType && addedItem.uniqueId) {
          console.log('📅 Work moved between calendar days, will be handled by onDragEnd');
        }
        else {
          console.log('⚠️ Unknown item type dropped:', addedItem.title || addedItem.name || `ID: ${addedItem.id}`);
        }
      }
      
      // Handle item removed (moved to another day)
      if (event.removed) {
        console.log('📤 Item removed from:', dateKey);
      }
      
      // Handle item moved within same day
      if (event.moved) {
        console.log('🔄 Item moved within same day:', dateKey);
      }
    },
    
    formatDateKey(date) {
      return date.toLocaleDateString('en-CA'); // YYYY-MM-DD format
    },
    
    getEventDates(event) {
      const dates = [];
      const start = new Date(event.start_time);
      const end = new Date(event.end_time);
      
      // Single day event
      if (start.toDateString() === end.toDateString()) {
        dates.push(this.formatDateKey(start));
      } else {
        // Multi-day event - add to each day
        const current = new Date(start);
        while (current <= end) {
          dates.push(this.formatDateKey(current));
          current.setDate(current.getDate() + 1);
        }
      }
      
      return dates;
    },
    
    getWorkDates(work) {
      const dates = [];
      const start = new Date(work.scheduled_start_date);
      const end = work.scheduled_end_date ? new Date(work.scheduled_end_date) : start;
      
      // Single day work
      if (start.toDateString() === end.toDateString()) {
        dates.push(this.formatDateKey(start));
      } else {
        // Multi-day work - add to each day
        const current = new Date(start);
        while (current <= end) {
          dates.push(this.formatDateKey(current));
          current.setDate(current.getDate() + 1);
        }
      }
      
      return dates;
    },

    
    // Drag and drop handlers
    onDragStart(event) {
      // Drag operation started
    },
    
    onDragEnd(event) {
      // Get the date from the destination element
      const toDate = event.to?.dataset?.date;
      const fromDate = event.from?.dataset?.date;
      
      if (!toDate || !fromDate || toDate === fromDate) {
        return;
      }
      
      // Get the dragged item data from the lists
      const newIndex = event.newIndex;
      const toDateItems = this.itemsByDateLocal[toDate];
      const draggedItem = toDateItems?.[newIndex];
      
      if (!draggedItem) {
        console.error('🗓️ ERROR: Could not identify dragged item');
        return;
      }
      
      // Call API for persistence
      this.persistDragOperation(draggedItem, fromDate, toDate);
    },
    
    // Schedule unscheduled work (from sidebar drop)
    async scheduleUnscheduledWork(item, toDate) {
      console.log('🚀 scheduleUnscheduledWork called with:', { 
        workId: item.id, 
        workTitle: item.title, 
        toDate: toDate,
        itemType: item.itemType 
      });
      
      try {
        if (item.itemType === 'work') {
          console.log('📅 Calling updateWorkDate store action...');
          // Schedule the work by updating its date
          await this.$store.dispatch('calendarStore/updateWorkDate', {
            workId: item.id,
            newDate: toDate
          });
          console.log('✅ updateWorkDate completed successfully');
        }
        
        console.log('🔄 Refreshing calendar data...');
        // Refresh calendar data to reflect server state
        await this.fetchCalendarData(this.currentDate);
        console.log('✅ Calendar data refreshed');
      } catch (error) {
        console.error('🗓️ ERROR: Failed to schedule item:', error);
        console.error('🗓️ ERROR Details:', error.response?.data);
        
        // Refresh calendar data to restore correct state
        this.fetchCalendarData(this.currentDate);
        // Note: Vuex store already handles error message display
      }
    },
    
    // API persistence for drag operations
    async persistDragOperation(item, fromDate, toDate) {
      try {
        if (item.itemType === 'event') {
          await this.rescheduleEvent(item.id, toDate);
        } else if (item.itemType === 'work') {
          await this.rescheduleWork(item.id, toDate);
        }
        
        // Refresh calendar data to reflect server state
        await this.fetchCalendarData(this.currentDate);
      } catch (error) {
        console.error('🗓️ ERROR: Drag persistence failed:', error);
        
        // Refresh calendar data to restore correct state
        this.fetchCalendarData(this.currentDate);
        // Note: Vuex store already handles error message display
      }
    },
    
    async rescheduleEvent(eventId, newDate) {
      // Call the existing Vuex action (updateEventDate)
      await this.$store.dispatch('calendarStore/updateEventDate', {
        eventId: eventId,
        newDate: newDate
      });
    },
    
    async rescheduleWork(workId, newDate) {
      // Call the existing Vuex action (updateWorkDate)
      await this.$store.dispatch('calendarStore/updateWorkDate', {
        workId: workId,
        newDate: newDate
      });
    },
    
    // Sidebar event handlers
    handleCreateWork() {
      this.showWorkForm = true;
    },
    
    handleCreateEvent() {
      this.selectedDate = null; // No pre-selected date when creating from sidebar
      this.showEventForm = true;
    },
    
    handleWorkClicked(work) {
      this.$store.dispatch('calendarStore/showItemDetail', { 
        item: work, 
        itemType: 'work' 
      });
    },
    
    handleEventClicked(event) {
      this.$store.dispatch('calendarStore/showItemDetail', { 
        item: event, 
        itemType: 'event' 
      });
    },
    
    
    eventAdded(event) {
      this.closeEventForm();
      // Refresh calendar data to show new event
      this.fetchCalendarData(this.currentDate);
    },
    
    workSaved() {
      this.closeWorkForm();
      // Refresh calendar data to show new work
      this.fetchCalendarData(this.currentDate);
    },
    
    handleItemUpdated() {
      // Refresh calendar data when item is updated
      this.fetchCalendarData(this.currentDate);
    },
    
    // Day click functionality
    handleDayClick(day, event) {
      if (!day.isCurrentMonth || !day.date) return;
      
      // Check if click was on an event/work item or create buttons
      if (event.target.closest('.calendar-event-item, .calendar-work-item, .global-day-create-buttons')) {
        return; // Let the item handler manage it
      }
      
      const dateKey = this.formatDateKey(day.date);
      
      // Toggle create buttons visibility for clicked day
      if (this.showCreateButtons === dateKey) {
        this.showCreateButtons = null;
        this.globalButtonsStyle = {};
      } else {
        this.showCreateButtons = dateKey;
        this.selectedDate = dateKey; // Store for button actions
        this.positionGlobalButtons(event.currentTarget);
      }
    },

    // Position global buttons relative to the clicked cell
    positionGlobalButtons(cellElement) {
      this.$nextTick(() => {
        if (!cellElement) return;
        
        const calendarContainer = this.$el;
        
        // Get cell position relative to the calendar container using offsetTop/offsetLeft
        // which accounts for scroll position and gives position relative to the positioned parent
        const cellOffsetTop = cellElement.offsetTop;
        const cellOffsetLeft = cellElement.offsetLeft;
        const cellWidth = cellElement.offsetWidth;
        const cellHeight = cellElement.offsetHeight;
        
        // Position buttons at center of the cell
        const top = cellOffsetTop + (cellHeight / 2);
        const left = cellOffsetLeft + (cellWidth / 2);
        
        // Set positioning style for global buttons
        this.globalButtonsStyle = {
          position: 'absolute',
          top: `${top}px`,
          left: `${left}px`,
          transform: 'translate(-50%, -50%)',
          zIndex: 1000
        };
      });
    },
    
    createEventForSelectedDay() {
      if (this.selectedDate) {
        this.showEventForm = true;
        this.showCreateButtons = null; // Hide buttons after selection
        this.globalButtonsStyle = {};
      }
    },
    
    createWorkForSelectedDay() {
      if (this.selectedDate) {
        this.showWorkForm = true;
        this.showCreateButtons = null; // Hide buttons after selection  
        this.globalButtonsStyle = {};
      }
    },
    
    // Legacy methods for backward compatibility
    createEventForDay(date) {
      this.selectedDate = this.formatDateKey(date);
      this.createEventForSelectedDay();
    },
    
    createWorkForDay(date) {
      this.selectedDate = this.formatDateKey(date);
      this.createWorkForSelectedDay();
    },
    
    handleGlobalClick(event) {
      // Hide create buttons when clicking outside calendar day cells and global buttons
      if (!event.target.closest('.calendar-day-cell, .global-day-create-buttons')) {
        this.showCreateButtons = null;
        this.globalButtonsStyle = {};
      }
    },
    
    handleEscKey(event) {
      if (event.key === 'Escape') {
        if (this.showEventForm) {
          this.closeEventForm();
        } else if (this.showWorkForm) {
          this.closeWorkForm();
        } else if (this.showCreateButtons) {
          this.showCreateButtons = null;
        }
      }
    },
    
    closeEventForm() {
      this.showEventForm = false;
      this.selectedDate = null;
    },
    
    closeWorkForm() {
      this.showWorkForm = false;
      this.selectedDate = null;
    },
  },
};
</script>

<style scoped>
.new-calendar-layout {
  display: grid;
  grid-template-columns: 0 1fr 17%; /* Left pane (hidden), main content, right sidebar */
  height: 100vh;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  gap: 10px;
  /* Position relative to allow absolute positioning of global buttons */
  position: relative;
}

.left-pane {
  /* No content or styles needed as it's a 0-width placeholder */
}

.calendar-main-content {
  display: flex;
  flex-direction: column;
  min-width: 0;
  width: 100%;
  max-width: 100%;
}

.right-sidebar-pane {
  padding: 0;
  min-width: 0;
  max-width: 100%;
  overflow: hidden;
}

.calendar-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0.5rem;
}

.nav-control {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.text-link {
  background: none;
  border: none;
  cursor: pointer;
  color: #3490dc;
  padding: 0.5rem;
  border-radius: 9999px;
}
.text-link:hover {
  background-color: #f1f5f9;
}

.days-of-week-header {
  display: grid;
  grid-template-columns: repeat(7, minmax(0, 1fr));
  text-align: center;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #6c757d;
  width: 100%;
  max-width: 100%;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, minmax(0, 1fr));
  flex-grow: 1;
  gap: 1px;
  border: 1px solid #dee2e6;
  background-color: #dee2e6;
  min-height: 0;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.calendar-day-cell {
  background-color: #ffffff;
  padding: 0.25rem;
  min-height: 0;
  min-width: 0;
  max-width: 100%;
  overflow: hidden;
  position: relative;
}

.calendar-day-cell.weekend {
  background-color: #f8f9fa;
  .day-number {
    color:red; 
  }
}

.calendar-day-cell.holiday {
  background-color: #f8f9fa;;
   .day-number {
    color:red; 
  }
}

.calendar-day-cell.not-current-month {
  background-color: #f8f9fa;
}

.calendar-day-cell.today {
  border: 2px solid #ffc107;
  position: relative;
}

.day-number-container {
  text-align: right;
}

.day-number {
  font-size: 0.875rem;
  font-weight: 500;
  color: #495057;
}

.day-content {
  min-height: 3rem;
  overflow-y: auto;
  max-height: calc(100% - 2rem); /* Leave space for day number */
}

/* Legacy day-create-buttons styles removed - now using global positioning system */

/* Drop zone styling */
.drop-zone {
  min-height: 100%;
  transition: background-color 0.2s ease;
  position: relative;
  z-index: 10;
}

.drop-zone.sortable-drag-over {
  background-color: rgba(52, 144, 220, 0.1);
}

/* Draggable item wrapper */
.draggable-item {
  cursor: move;
  transition: transform 0.2s ease;
}

.draggable-item:hover {
  transform: translateX(2px);
}

/* Dragging states */
.sortable-ghost {
  opacity: 0.4;
}

.sortable-drag {
  opacity: 0;
}

/* Large screens */
@media (min-width: 1200px) {
  .new-calendar-layout {
    grid-template-columns: 0 1fr 20%; /* Smaller sidebar on large screens */
  }
}

/* Medium-large screens */
@media (max-width: 1199px) and (min-width: 1025px) {
  .new-calendar-layout {
    grid-template-columns: 0 1fr 25%; /* Slightly larger sidebar */
  }
}

/* Tablet view */
@media (max-width: 1024px) and (min-width: 769px) {
  .new-calendar-layout {
    grid-template-columns: 0 1fr 25%; /* Calendar and sidebar */
  }
}

/* Small tablet view */
@media (max-width: 1024px) and (min-width: 481px) {
  .new-calendar-layout {
    grid-template-columns: 1fr; /* Stack on small tablets too */
    grid-template-rows: auto auto;
    gap: 1rem;
    height: 100vh;
  }

  .calendar-main-content {
    order: 1;
    grid-row: 1;
  }

  .right-sidebar-pane {
    order: 2;
    grid-row: 2;
    border-left: none;
    border-top: 1px solid #dee2e6;
    margin-top: 0;
    padding: 1.5rem;
  }

  .left-pane {
    display: none;
  }
}


/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background: white;
  border-radius: 8px;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #dee2e6;
  background: #f8f9fa;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  line-height: 1;
}

.close-btn:hover {
  color: #495057;
}

.central-modal-content {
  padding: 1.5rem;
  max-height: calc(90vh - 80px);
  overflow-y: auto;
}

/* Mobile view */
@media (max-width: 480px) {
  .new-calendar-layout {
    grid-template-columns: 1fr; /* Stack calendar and sidebar */
    grid-template-rows: auto auto; /* Two rows: calendar then sidebar */
    gap: 1rem;
  }

  .calendar-main-content {
    order: 1; /* Calendar first */
    grid-row: 1;
  }

  .right-sidebar-pane {
    order: 2; /* Sidebar second (below calendar) */
    grid-row: 2;
    border-left: none;
    border-top: 1px solid #dee2e6;
    margin-top: 0;
    padding: 1rem;
  }

  .left-pane {
    display: none; /* Hide empty left pane on mobile */
  }
}

/* Global Create Buttons - positioned outside grid to prevent clipping */
.global-day-create-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  background: #ffffff;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 120px;
  box-sizing: border-box;
  /* Will be positioned dynamically via JavaScript */
  pointer-events: all;
}

.global-day-create-buttons button {
  white-space: nowrap;
  font-size: 0.875rem;
}
</style>