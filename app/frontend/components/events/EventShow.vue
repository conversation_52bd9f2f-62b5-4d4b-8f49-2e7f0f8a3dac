<template>
  <div class="calendar-event-item" :class="[statusClass, { 
    'needs-attention': needsAttention,
    'event-rejected': event.status === 'rejected'
  }]">
    <div class="event-icon">
      <BellRing v-if="event.event_type === 'vacation' && event.status === 'pending'" :size="14" />
      <User v-else :size="14" />
    </div>
    <div class="event-content">
      <div class="event-name" :class="{ 'rejected-text': event.status === 'rejected' }">
        {{ event.event_type ? $t('event_type.' + event.event_type) : (event.title || event.name || 'No name') }}
      </div>
      <div v-if="event.contract && (event.contract.first_name || event.contract.last_name)" 
           class="event-title" :class="{ 'rejected-text': event.status === 'rejected' }">
        {{ event.contract.first_name }} {{ event.contract.last_name }}
      </div>
    </div>
    
    <!-- Status display - only show for vacation events -->
    <div v-if="event.event_type === 'vacation' && event.status === 'pending'" class="status-badge">
      {{ getStatusText(event.status) }}
    </div>
    <div v-else-if="event.event_type === 'vacation' && event.status === 'approved'" class="status-icon">
      <CheckCheck :size="14" />
    </div>
    
    <div class="dropdown" v-if="hasDropdownActions">
      <button class="dropdown-toggle p-1 text-gray-500 hover:text-gray-700" @click="toggleDropdown">
        <MoreVertical :size="18" />
      </button>
      <div class="dropdown-menu" v-if="showDropdown">
        <a v-if="canApproveEvent" 
           href="#" 
           class="dropdown-item" 
           @click.prevent="approveEvent">
          {{ $t('events.approve', 'Schválit') }}
        </a>
        <a v-if="canDeleteEvent" href="#" class="dropdown-item" @click.prevent="confirmDelete">{{ $t('delete', 'Smazat') }}</a>
      </div>
    </div>
  </div>
</template>

<script>
import { Calendar, Clipboard, MapPin, MoreVertical, HelpCircle, CheckCheck, Clock, CalendarDays, BellRing, User } from 'lucide-vue-next';
import { getLocaleString } from '../../utils/dateFormatter';
import authorizationMixin from '../../mixins/authorizationMixin';

export default {
  components: {
    MapPin, Clipboard, MoreVertical, Calendar, HelpCircle, CheckCheck, Clock, CalendarDays, BellRing, User
  },
  mixins: [authorizationMixin],
  props: {
    event: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      showDropdown: false, 
      isEditing: false
    };
  },
  computed: {
    statusClass() {
      // Base class for all events
      const baseClass = 'event-base';

      // Check if event is rejected
      if (this.event.status === 'rejected') {
        return `${baseClass} event-rejected-status`;
      }

      // Check if event is cancelled or finished
      if (this.event.status === 'cancelled' || this.event.status === 'finished') {
        return `${baseClass} event-inactive`;
      }

      return `${baseClass} event-active`;
    },

    needsAttention() {
      // Events that are not approved should shine/highlight
      return this.event.status === 'pending' || this.event.status === 'draft';
    },
    
    canDeleteEvent() {
      // Only users can delete their own events, even managers cannot delete other users' events
      const isOwnEvent = this.event.contract && this.event.contract.id === this.$store.state.calendarStore.currentUserContractId;
      return isOwnEvent;
    },
    
    canApproveEvent() {
      // Only managers can approve, and only vacation events need approval
      if (!this.isManager) return false;
      
      // Only show approve for vacation events with pending status
      return this.event.event_type === 'vacation' && this.event.status === 'pending';
    },
    
    hasDropdownActions() {
      // Show dropdown button only if there are available actions
      return this.canDeleteEvent || this.canApproveEvent;
    }
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    handleClickOutside(event) {
      if (this.showDropdown && this.$el && !this.$el.contains(event.target)) {
        this.showDropdown = false;
        this.$el.classList.remove('dropdown-open');
      }
    },
    toggleDropdown() {
      this.showDropdown = !this.showDropdown;
      
      // Add/remove class for z-index management
      if (this.showDropdown) {
        this.$el.classList.add('dropdown-open');
        this.$nextTick(() => {
          this.positionDropdown();
        });
      } else {
        this.$el.classList.remove('dropdown-open');
      }
    },
    
    positionDropdown() {
      // Find the dropdown menu element
      const dropdownMenu = this.$el.querySelector('.dropdown-menu');
      if (!dropdownMenu) return;
      
      const rect = dropdownMenu.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      
      // If dropdown would extend below viewport, show it above instead
      if (rect.bottom > viewportHeight && rect.top > dropdownMenu.offsetHeight) {
        // Already positioned above by CSS
        dropdownMenu.style.bottom = '100%';
        dropdownMenu.style.top = 'auto';
      } else if (rect.top < 0) {
        // If showing above would go off screen, show below
        dropdownMenu.style.bottom = 'auto';
        dropdownMenu.style.top = '100%';
      }
    },
    getStatusText(status) {
      switch(status) {
        case 'pending': return this.$t('statuses.pending_approval', 'Čeká na schválení');
        case 'approved': return this.$t('statuses.approved', 'Schváleno');
        case 'cancelled': return this.$t('statuses.cancelled', 'Zrušeno');
        default: return status;
      }
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      const locale = getLocaleString(this.$i18n.locale);
      return date.toLocaleDateString(locale);
    },
    formatTime(timeString) {
      const date = new Date(timeString);
      const hours = date.getHours();
      const minutes = date.getMinutes();
      if (hours === 0 && minutes === 0) {
        return '';
      }
      const locale = getLocaleString(this.$i18n.locale);
      return date.toLocaleTimeString(locale, {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    },
    confirmDelete() {
      // No confirm dialog here - parent handles it
      this.$emit('delete-requested', this.event.id);
      this.showDropdown = false;
    },
    async approveEvent() {
      try {
        // Use the calendarStore confirmEvent action (same endpoint as ownerStore)
        await this.$store.dispatch('calendarStore/confirmEvent', this.event.id);
        
        // Update local event status immediately for better UX
        this.event.status = 'approved';
        
        this.$emit('event-approved', this.event.id);
        this.showDropdown = false;
      } catch (error) {
        console.error('Error approving event:', error);
        // Error handling is done in the store action
      }
    }
  }
};
</script>

<style scoped>
/* Matching CalendarEventItem design */
.calendar-event-item {
  position: relative;
  padding: 0.25rem;
  margin-bottom: 0.125rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: transform 0.2s ease;
  display: flex;
  align-items: flex-start;
  min-height: 1.75rem;
  overflow: visible; /* Allow dropdown to show outside */
  gap: 0.15rem;
  border: 1px solid #faaab2;
  background-color: white;
  width: 90%;
  min-width: 350px;
  z-index: 1; /* Base z-index */
}

/* When dropdown is open, increase z-index of the parent item */
.calendar-event-item:has(.dropdown-menu) {
  z-index: 1001;
}

.calendar-event-item:hover {
  transform: translateY(1px);
}

.event-base {
  background-color: white;
  color: #333;
}

.event-active {
  background-color: white;
  color: #333;
}

.event-inactive {
  background-color: white;
  color: #777;
}

.calendar-event-item.needs-attention {
  --attention-bg: #fefce8;
  --attention-border: #facc15;
  color: #3f3f46;
  border-left: 3px solid var(--attention-border);
  background-color: var(--attention-bg);
}

.event-icon {
  display: flex;
  align-items: center;
  margin-top: 0.125rem;
}

.event-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  min-width: 0;
}

.event-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  font-weight: 600;
}

.event-title {
  font-size: 0.75rem;
  opacity: 0.9;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.rejected-text {
  text-decoration: line-through;
  color: #757575 !important;
}

.event-rejected-status {
  background-color: #ffebee;
}

.status-badge {
  font-size: 0.6rem;
  font-weight: 600;
  color: #f59e0b;
  text-transform: uppercase;
  background-color: #fefce8;
  padding: 1px 4px;
  border-radius: 2px;
  align-self: flex-start;
  margin-top: 0.125rem;
}

.status-icon {
  display: flex;
  align-items: center;
  margin-top: 0.125rem;
  color: #22c55e;
}

.dropdown {
  position: relative;
  margin-left: auto;
  z-index: 100; /* Ensure dropdown is above other elements */
}

/* Alternative for browsers that don't support :has() */
.calendar-event-item.dropdown-open {
  z-index: 1001;
}

.dropdown-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  color: #6b7280;
}

.dropdown-toggle:hover {
  color: #4b5563;
}

.dropdown-menu {
  position: absolute;
  right: 0;
  top: 100%; /* Position below the dropdown button */
  margin-top: 4px; /* Small gap between dropdown and button */
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  z-index: 1000;
  min-width: 120px;
}

.dropdown-item {
  display: block;
  padding: 0.5rem 1rem;
  text-decoration: none;
  color: #333;
  font-size: 0.875rem;
}

.dropdown-item:hover {
  background: #f5f5f5;
}

/* Alternative positioning when dropdown should appear below */
.dropdown-menu.show-below {
  bottom: auto;
  top: 100%;
  margin-bottom: 0;
  margin-top: 4px;
}

.icon {
  width: 1rem;
  height: 1rem;
}

/* Mobile responsive dropdown */
@media (max-width: 768px) {
  .dropdown-menu {
    right: -10px; /* Offset to prevent edge clipping */
    min-width: 140px;
    font-size: 0.9rem;
  }
  
  .dropdown-item {
    padding: 0.75rem 1rem; /* Larger touch targets */
  }
  
  .calendar-event-item {
    margin-bottom: 0.25rem; /* More space between items for dropdown visibility */
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .dropdown-menu {
    right: -20px;
    min-width: 160px;
  }
}
</style>